<div class="flex items-center mb-6">
  <%= link_to_back pay_withdraws_path %>
  <h1 class="text-xl">
    <%= @withdraw.new_record? ? "添加提现订单" : (@withdraw.pending? ? "处理提现申请" : "查看提现详情") %>
  </h1>
</div>

<% if @withdraw.new_record? %>
  <!-- 新建提现订单的表单 -->
  <%= form_with(model: [:pay, @withdraw], class: "space-y-6", data: { turbo: false }) do |form| %>
    <%= render "shared/error_messages", object: @withdraw %>
    <div class="space-y-4">
      <div>
        <%= form.label :user_id, "用户ID *", class: "block text-sm font-medium text-gray-700" %>
        <%= form.text_field :user_id, class: "input" %>
      </div>
      <div>
        <%= form.label :amount, "提现金额 *", class: "block text-sm font-medium text-gray-700" %>
        <%= form.text_field :amount, class: "input" %>
      </div>
      <div>
        <%= form.label :fee, "费率 *", class: "block text-sm font-medium text-gray-700" %>
        <%= form.text_field :fee, class: "input" %>
      </div>
      <div>
        <%= form.label :real_amount, "实际金额 *", class: "block text-sm font-medium text-gray-700" %>
        <%= form.text_field :real_amount, class: "input" %>
      </div>
      <div>
        <%= form.label :order_no, "提现订单号 *", class: "block text-sm font-medium text-gray-700" %>
        <%= form.text_field :order_no, class: "input" %>
      </div>
    </div>
    <div class="flex justify-end gap-4">
      <%= link_to "返回", pay_withdraws_path, class: "btn btn-secondary" %>
      <%= form.submit class: "btn btn-primary" %>
    </div>
  <% end %>
<% else %>
  <!-- 编辑/查看提现订单 -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- 基本信息 -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">订单号:</span>
          <span class="text-sm text-gray-900 font-mono"><%= @withdraw.order_no %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">申请金额:</span>
          <span class="text-sm text-gray-900 font-semibold">¥<%= @withdraw.amount %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">手续费:</span>
          <span class="text-sm text-gray-900">¥<%= @withdraw.fee %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">实际金额:</span>
          <span class="text-sm text-gray-900 font-semibold text-green-600">¥<%= @withdraw.real_amount %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">申请时间:</span>
          <span class="text-sm text-gray-900"><%= format_time(@withdraw.created_at) %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">申请IP:</span>
          <span class="text-sm text-gray-900 font-mono"><%= @withdraw.ip %></span>
        </div>
        <% if @withdraw.payment_order_no.present? %>
          <div class="flex justify-between">
            <span class="text-sm font-medium text-gray-500">支付订单号:</span>
            <span class="text-sm text-gray-900 font-mono"><%= @withdraw.payment_order_no %></span>
          </div>
        <% end %>
      </div>
    </div>

    <!-- 用户信息 -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">用户信息</h3>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">用户ID:</span>
          <span class="text-sm text-gray-900"><%= @withdraw.user_id %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">用户名:</span>
          <span class="text-sm text-gray-900"><%= @withdraw.username %></span>
        </div>
        <% if @withdraw.user %>
          <div class="flex justify-between">
            <span class="text-sm font-medium text-gray-500">昵称:</span>
            <span class="text-sm text-gray-900"><%= @withdraw.user.nickname %></span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm font-medium text-gray-500">手机号:</span>
            <span class="text-sm text-gray-900"><%= @withdraw.user.mobile %></span>
          </div>
        <% end %>
        <% if @withdraw.agent %>
          <div class="flex justify-between">
            <span class="text-sm font-medium text-gray-500">总代:</span>
            <span class="text-sm text-gray-900"><%= @withdraw.agent.name %></span>
          </div>
        <% end %>
        <% if @withdraw.channel %>
          <div class="flex justify-between">
            <span class="text-sm font-medium text-gray-500">渠道:</span>
            <span class="text-sm text-gray-900"><%= @withdraw.channel.name %></span>
          </div>
        <% end %>
        <% if @withdraw.bank_card %>
          <div class="pt-2 border-t border-gray-200">
            <span class="text-sm font-medium text-gray-500">银行卡信息:</span>
            <div class="mt-1 text-sm text-gray-900">
              <div><%= @withdraw.bank_card.card_bank %></div>
              <div class="font-mono">**** **** **** <%= @withdraw.bank_card.card_number&.last(4) %></div>
              <div><%= @withdraw.bank_card.card_username %></div>
              <div class="text-xs text-gray-500"><%= @withdraw.bank_card.card_branch %></div>
            </div>
          </div>
        <% end %>
        <% if @withdraw.payment %>
          <div class="pt-2 border-t border-gray-200">
            <span class="text-sm font-medium text-gray-500">支付通道:</span>
            <div class="mt-1 text-sm text-gray-900">
              <div><%= @withdraw.payment.name %></div>
              <div class="text-xs text-gray-500">费率: <%= @withdraw.payment.withdraw_fee_rate %>%</div>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">状态信息</h3>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">当前状态:</span>
          <span class="text-sm">
            <% case @withdraw.status %>
              <% when 'pending' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">待提现</span>
              <% when 'success' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">提现成功</span>
              <% when 'rejected' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">拒绝</span>
              <% when 'confiscated' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">没收</span>
              <% when 'unknown' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">未知</span>
            <% end %>
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">风控状态:</span>
          <span class="text-sm text-gray-900">
            <%= @withdraw.risk == 1 ? "风控" : "正常" %>
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">审核时间:</span>
          <span class="text-sm text-gray-900"><%= format_time(@withdraw.review_time) %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">处理时间:</span>
          <span class="text-sm text-gray-900"><%= format_time(@withdraw.process_time) %></span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">完成时间:</span>
          <span class="text-sm text-gray-900"><%= format_time(@withdraw.finish_time) %></span>
        </div>
        <% if @withdraw.remark.present? %>
          <div class="pt-2 border-t border-gray-200">
            <span class="text-sm font-medium text-gray-500">备注:</span>
            <p class="text-sm text-gray-900 mt-1"><%= @withdraw.remark %></p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- 处理表单 -->
  <% if @withdraw.pending? %>
    <div class="bg-white border border-gray-200 rounded-lg p-6 mt-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">处理提现申请</h3>
      <%= form_with(model: [:pay, @withdraw], class: "space-y-4", data: { turbo: false }) do |form| %>
        <%= render "shared/error_messages", object: @withdraw %>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <%= form.label :status, "处理结果 *", class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :status, options_for_select([
              ['提现成功', 'success'],
              ['拒绝', 'rejected'],
              ['没收', 'confiscated']
            ], @withdraw.status), { prompt: '请选择处理结果' }, { class: "select select-bordered w-full" } %>
          </div>

          <div>
            <%= form.label :risk, "风控状态", class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :risk, options_for_select([
              ['正常', 0],
              ['风控', 1]
            ], @withdraw.risk), {}, { class: "select select-bordered w-full" } %>
          </div>
        </div>

        <div>
          <%= form.label :remark, "处理备注", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_area :remark, class: "textarea textarea-bordered w-full", rows: 3, placeholder: "请输入处理备注..." %>
        </div>

        <div class="flex justify-end gap-4 pt-4">
          <%= link_to "返回", pay_withdraws_path, class: "btn btn-secondary" %>
          <%= form.submit "确认处理", class: "btn btn-primary", data: { confirm: "确认要处理这个提现申请吗？" } %>
        </div>
      <% end %>
    </div>
  <% else %>
    <!-- 已处理的订单只显示返回按钮 -->
    <div class="flex justify-end gap-4 mt-6">
      <%= link_to "返回", pay_withdraws_path, class: "btn btn-secondary" %>
    </div>
  <% end %>
<% end %>