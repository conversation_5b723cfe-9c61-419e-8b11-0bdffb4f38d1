if Rails.env.test?
  # 在测试环境中创建一个模拟的 DynamoDB 客户端
  mock_client = Object.new
  def mock_client.batch_write_item(params)
    { unprocessed_items: {} }
  end
  def mock_client.put_item(params)
    { consumed_capacity: nil }
  end
  def mock_client.query(params)
    { items: [], last_evaluated_key: nil }
  end
  def mock_client.create_table(params)
    { table_description: { table_status: "ACTIVE" } }
  end
  def mock_client.wait_until(condition, params)
    true
  end
  def mock_client.update_time_to_live(params)
    { time_to_live_specification: { enabled: true } }
  end

  DYNAMO_DB_CLIENT = mock_client
  puts "Mock DynamoDB Client initialized for test environment."
else
  access_key_id = ENV["DYNAMODB_AWS_ACCESS_KEY_ID"].presence || Rails.application.credentials.dig(:aws, :dynamodb, :access_key_id)
  secret_access_key = ENV["DYNAMODB_AWS_SECRET_ACCESS_KEY"].presence || Rails.application.credentials.dig(:aws, :dynamodb, :secret_access_key)
  region = ENV["DYNAMODB_AWS_REGION"].presence || Rails.application.credentials.dig(:aws, :dynamodb, :region)

  if access_key_id.present? && secret_access_key.present? && region.present?
    Aws.config.update({
      region: region,
      credentials: Aws::Credentials.new(access_key_id, secret_access_key)
    })

    DYNAMO_DB_CLIENT = Aws::DynamoDB::Client.new
    puts "Aws DynamoDB Client initialized."
  else
    puts "No Aws DynamoDB Environment variables detect."
  end
end
