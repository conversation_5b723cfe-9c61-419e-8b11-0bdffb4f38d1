require "test_helper"

class RedisStreamProcessorTest < ActiveSupport::TestCase
  setup do
    # 为每个测试使用唯一的 stream 名称避免并行测试冲突
    @test_id = SecureRandom.hex(8)
    @test_stream_key = "test_stream_#{@test_id}"
    @test_group_name = "test_group_#{@test_id}"
    @test_consumer_name = "test_consumer_#{@test_id}"

    # 保存原始常量值
    @original_stream_key = RedisStreamProcessor::STREAM_KEY
    @original_group_name = RedisStreamProcessor::GROUP_NAME
    @original_consumer_name = RedisStreamProcessor::CONSUMER_NAME

    # 临时修改常量用于测试（避免重复定义警告）
    RedisStreamProcessor.send(:remove_const, :STREAM_KEY)
    RedisStreamProcessor.send(:remove_const, :GROUP_NAME)
    RedisStreamProcessor.send(:remove_const, :CONSUMER_NAME)

    RedisStreamProcessor.const_set(:STREAM_KEY, @test_stream_key)
    RedisStreamProcessor.const_set(:GROUP_NAME, @test_group_name)
    RedisStreamProcessor.const_set(:CONSUMER_NAME, @test_consumer_name)

    @processor = RedisStreamProcessor.new

    # 确保每个测试开始时都有干净的 Redis Stream 环境
    Rails.cache.redis.with do |redis_client|
      begin
        # 删除可能存在的 stream
        redis_client.del(@test_stream_key)
        # 创建新的 stream 和 consumer group
        redis_client.xgroup(:create, @test_stream_key, @test_group_name, "0", mkstream: true)
      rescue => e
        puts "Setup failed: #{e.message}"
      end
    end
  end

  teardown do
    # 停止处理器
    @processor.stop if @processor

    # 清理测试数据
    Rails.cache.redis.with do |redis_client|
      begin
        # 删除 stream（这会自动删除相关的 consumer group）
        redis_client.del(@test_stream_key)
      rescue => e
        puts "Teardown failed: #{e.message}"
      end
    end

    # 恢复原始常量
    RedisStreamProcessor.send(:remove_const, :STREAM_KEY)
    RedisStreamProcessor.send(:remove_const, :GROUP_NAME)
    RedisStreamProcessor.send(:remove_const, :CONSUMER_NAME)

    RedisStreamProcessor.const_set(:STREAM_KEY, @original_stream_key)
    RedisStreamProcessor.const_set(:GROUP_NAME, @original_group_name)
    RedisStreamProcessor.const_set(:CONSUMER_NAME, @original_consumer_name)
  end

  test "should check pending messages when needed" do
    # 初始状态需要检查（因为 @last_pending_check 为 nil）
    assert @processor.send(:should_check_pending_messages?)

    # 设置上次检查时间为刚刚
    @processor.instance_variable_set(:@last_pending_check, Time.current)
    assert_not @processor.send(:should_check_pending_messages?)

    # 设置上次检查时间为很久以前
    @processor.instance_variable_set(:@last_pending_check, Time.current - 400)
    assert @processor.send(:should_check_pending_messages?)
  end

  test "should process pending messages" do
    # 添加测试消息
    entry_id = nil
    Rails.cache.redis.with do |redis_client|
      entry_id = redis_client.xadd(RedisStreamProcessor::STREAM_KEY, {
        user_id: "1",
        bet: "100",
        real_win: "80",
        win: "80",
        game_number: "test_game",
        round_id: "test_round",
        before_money: "1000",
        after_money: "1080",
        bet_time: Time.current.to_i.to_s
      })
    end
    assert entry_id, "Failed to add test message to stream"

    # 读取消息但不确认（模拟消费者崩溃）
    Rails.cache.redis.with do |redis_client|
      messages = redis_client.xreadgroup(
        RedisStreamProcessor::GROUP_NAME,
        RedisStreamProcessor::CONSUMER_NAME,
        RedisStreamProcessor::STREAM_KEY,
        ">",
        count: 1
      )
      assert_not_empty messages, "Failed to read message from stream"
    end

    # 临时修改 PENDING_TIMEOUT 为更短的时间用于测试
    original_timeout = RedisStreamProcessor::PENDING_TIMEOUT
    RedisStreamProcessor.send(:remove_const, :PENDING_TIMEOUT)
    RedisStreamProcessor.const_set(:PENDING_TIMEOUT, 1) # 1秒超时

    begin
      # 等待消息变为pending状态并超时
      sleep 2

      # Mock DynamodbLogger.log_bet method
      original_method = DynamodbLogger.method(:log_bet)
      DynamodbLogger.define_singleton_method(:log_bet) do |**args|
        true # 返回成功
      end

      begin
        # 处理pending消息
        assert_nothing_raised do
          @processor.send(:process_pending_messages)
        end
      ensure
        # 恢复原始方法
        DynamodbLogger.define_singleton_method(:log_bet, original_method)
      end
    ensure
      # 恢复原始超时时间
      RedisStreamProcessor.send(:remove_const, :PENDING_TIMEOUT)
      RedisStreamProcessor.const_set(:PENDING_TIMEOUT, original_timeout)
    end

    # 验证消息已被确认（pending列表应该为空）
    Rails.cache.redis.with do |redis_client|
      pending_messages = redis_client.xpending(
        RedisStreamProcessor::STREAM_KEY,
        RedisStreamProcessor::GROUP_NAME,
        "-", "+", 100
      )
      assert_empty pending_messages, "Pending messages should be empty after processing"
    end
  end

  test "should handle missing message details gracefully" do
    # 创建一个不存在的entry_id
    fake_entry_id = "9999999999999-0"

    # Mock DynamodbLogger.log_bet method
    original_method = DynamodbLogger.method(:log_bet)
    DynamodbLogger.define_singleton_method(:log_bet) do |**args|
      true # 返回成功
    end

    begin
      # 这个测试验证当消息详情不存在时的处理
      assert_nothing_raised do
        @processor.send(:process_pending_message, fake_entry_id, {
          "user_id" => "1",
          "bet" => "100",
          "real_win" => "80",
          "win" => "80",
          "game_number" => "test",
          "round_id" => "test",
          "before_money" => "1000",
          "after_money" => "1080",
          "bet_time" => Time.current.to_i.to_s
        })
      end
    ensure
      # 恢复原始方法
      DynamodbLogger.define_singleton_method(:log_bet, original_method)
    end
  end

  test "should handle redis errors gracefully" do
    # 使用不存在的stream测试错误处理
    original_stream_key = RedisStreamProcessor::STREAM_KEY

    # 临时修改常量
    RedisStreamProcessor.send(:remove_const, :STREAM_KEY)
    RedisStreamProcessor.const_set(:STREAM_KEY, "nonexistent_stream")

    assert_nothing_raised do
      @processor.send(:process_pending_messages)
    end

    # 恢复原始stream key
    RedisStreamProcessor.send(:remove_const, :STREAM_KEY)
    RedisStreamProcessor.const_set(:STREAM_KEY, original_stream_key)
  end

  test "should stop gracefully" do
    assert @processor.instance_variable_get(:@running)

    @processor.stop

    assert_not @processor.instance_variable_get(:@running)
  end
end
