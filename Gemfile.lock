GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    airbrussh (1.5.3)
      sshkit (>= 1.6.1, != 1.7.0)
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1111.0)
    aws-sdk-core (3.225.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-dynamodb (1.145.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-kms (1.102.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.189.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    capistrano (3.19.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.1)
      capistrano (~> 3.1)
    capistrano-git-with-submodules (2.0.6)
      capistrano (~> 3.7)
    capistrano-rails (1.7.0)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    carrierwave (3.1.2)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    cronex (0.15.0)
      tzinfo
      unicode (>= *******)
    csv (3.3.5)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    dotenv (3.1.8)
    drb (2.2.3)
    erb (5.0.1)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    excon (1.2.7)
      logger
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-aarch64-linux-musl)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm-linux-musl)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi (1.17.2-x86_64-linux-musl)
    fog-aws (3.32.0)
      base64 (~> 0.2.0)
      fog-core (~> 2.6)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.6.0)
      builder
      excon (~> 1.0)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.5)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (1.1.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.12.2)
    jwt (2.10.1)
      base64
    language_server-protocol (********)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0603)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_json (1.15.0)
    mysql2 (0.5.6)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    ostruct (0.6.1)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    requestjs-rails (0.0.13)
      railties (>= 7.1.0)
    rotp (6.3.0)
    rubocop (1.76.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.0)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    securerandom (0.4.1)
    sidekiq (8.0.4)
      connection_pool (>= 2.5.0)
      json (>= 2.9.0)
      logger (>= 1.6.2)
      rack (>= 3.1.0)
      redis-client (>= 0.23.2)
    sidekiq-cron (2.3.0)
      cronex (>= 0.13.0)
      fugit (~> 1.8, >= 1.11.1)
      globalid (>= 1.0.1)
      sidekiq (>= 6.5.0)
    sqids (0.2.2)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    ssrf_filter (1.3.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    tailwindcss-rails (4.2.3)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 4.0)
    tailwindcss-ruby (4.1.8)
    tailwindcss-ruby (4.1.8-aarch64-linux-gnu)
    tailwindcss-ruby (4.1.8-aarch64-linux-musl)
    tailwindcss-ruby (4.1.8-arm64-darwin)
    tailwindcss-ruby (4.1.8-x86_64-darwin)
    tailwindcss-ruby (4.1.8-x86_64-linux-gnu)
    tailwindcss-ruby (4.1.8-x86_64-linux-musl)
    thor (1.3.2)
    thruster (0.1.13)
    thruster (0.1.13-aarch64-linux)
    thruster (0.1.13-arm64-darwin)
    thruster (0.1.13-x86_64-darwin)
    thruster (0.1.13-x86_64-linux)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode (*******)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  x86_64-darwin
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  aws-sdk-dynamodb
  aws-sdk-s3
  bcrypt (~> 3.1.7)
  bootsnap
  brakeman
  capistrano (~> 3.19)
  capistrano-bundler (~> 2.1)
  capistrano-git-with-submodules (~> 2.0)
  capistrano-rails (~> 1.7)
  capistrano-rbenv (~> 2.2)
  carrierwave (~> 3.1)
  csv
  debug
  dotenv
  fog-aws
  image_processing (~> 1.14)
  importmap-rails
  jbuilder
  jwt
  mysql2 (~> 0.5)
  pagy
  propshaft
  puma (>= 5.0)
  rack-cors
  rails (~> 8.0.2)
  redis (~> 5.4)
  requestjs-rails
  rotp (~> 6.3)
  rubocop-rails-omakase
  sidekiq (~> 8.0)
  sidekiq-cron
  sqids
  stimulus-rails
  tailwindcss-rails (~> 4.2)
  tailwindcss-ruby (~> 4.1)
  thruster
  turbo-rails
  tzinfo-data

BUNDLED WITH
   2.6.9
